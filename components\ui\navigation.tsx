"use client"

import { useState, useEffect, useRef } from "react"
import { gsap } from "gsap"
import { Button } from "@/components/ui/button"
import { Scale, Menu, X } from "lucide-react"

export function Navigation() {
  const [isOpen, setIsOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const navRef = useRef<HTMLDivElement>(null)

  const navItems = [
    { name: "Features", href: "#features" },
    { name: "Pricing", href: "#pricing" },
    { name: "About", href: "#about" },
    { name: "Contact", href: "#contact" },
  ]

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 50)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  useEffect(() => {
    const ctx = gsap.context(() => {
      gsap.fromTo(
        navRef.current,
        { y: -20, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, ease: "power3.out", delay: 0.5 },
      )
    }, navRef)

    return () => ctx.revert()
  }, [])

  return (
    <div className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-4xl px-6">
      <div
        ref={navRef}
        className={`relative rounded-full transition-all duration-500 ${
          scrolled
            ? "bg-white/20 dark:bg-neutral-900/20 backdrop-blur-2xl border border-white/30 dark:border-neutral-700/30 shadow-2xl shadow-black/10"
            : "bg-white/10 dark:bg-neutral-900/10 backdrop-blur-xl border border-white/20 dark:border-neutral-700/20"
        }`}
      >
        {/* Gradient overlay */}
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-white/5 via-transparent to-white/5 dark:from-neutral-800/5 dark:via-transparent dark:to-neutral-800/5" />

        <div className="relative flex items-center justify-between px-8 py-4">
          {/* Logo */}
          <div className="flex items-center gap-3 group cursor-pointer">
            <div className="p-2 rounded-xl bg-white/20 dark:bg-neutral-800/20 backdrop-blur-sm group-hover:bg-white/30 dark:group-hover:bg-neutral-700/30 transition-all duration-300">
              <Scale className="w-5 h-5 text-neutral-800 dark:text-white" />
            </div>
            <span className="text-lg font-light text-neutral-800 dark:text-white">
              Legal<span className="font-extralight">AI</span>
            </span>
          </div>

          {/* Desktop Menu */}
          <div className="hidden md:flex items-center gap-8">
            {navItems.map((item) => (
              <a
                key={item.name}
                href={item.href}
                className="relative text-sm font-light text-neutral-700 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-white transition-all duration-300 group"
              >
                {item.name}
                <span className="absolute -bottom-1 left-0 w-0 h-px bg-neutral-900 dark:bg-white group-hover:w-full transition-all duration-300" />
              </a>
            ))}
          </div>

          {/* Desktop CTA */}
          <div className="hidden md:flex items-center gap-4">
            <a 
              href="/login" 
              className="text-sm font-light text-neutral-700 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-white transition-colors"
            >
              Sign In
            </a>

            <a href="/dashboard">
              <Button className="bg-neutral-900/80 hover:bg-neutral-900 dark:bg-white/80 dark:hover:bg-white text-white dark:text-neutral-900 rounded-full px-6 py-2 text-sm font-light backdrop-blur-sm border-0 hover:scale-105 transition-all duration-300">
                Get Started
              </Button>
            </a>
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden p-2 hover:bg-white/20 dark:hover:bg-neutral-800/20 rounded-full transition-colors duration-300"
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </Button>
        </div>

        {/* Mobile Menu */}
        {isOpen && (
          <div className="md:hidden absolute top-full left-0 right-0 mt-2 rounded-2xl bg-white/20 dark:bg-neutral-900/20 backdrop-blur-2xl border border-white/30 dark:border-neutral-700/30 shadow-2xl shadow-black/10">
            <div className="p-6">
              <div className="flex flex-col gap-4">
                {navItems.map((item) => (
                  <a
                    key={item.name}
                    href={item.href}
                    className="text-neutral-700 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-white transition-colors font-light"
                    onClick={() => setIsOpen(false)}
                  >
                    {item.name}
                  </a>
                ))}

                <div className="flex flex-col gap-3 pt-4 border-t border-white/20 dark:border-neutral-700/20">
                  <a 
                    href="/login" 
                    className="text-left text-neutral-700 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-white transition-colors font-light"
                    onClick={() => setIsOpen(false)}
                  >
                    Sign In
                  </a>

                  <Button className="bg-neutral-900/80 text-white dark:bg-white/80 dark:text-neutral-900 rounded-full px-6 py-2 text-sm font-light backdrop-blur-sm border-0 w-fit">
                    Get Started
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
