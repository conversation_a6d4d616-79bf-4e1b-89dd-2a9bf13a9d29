"use client"

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "@/hooks/use-toast"
import { useTheme } from "next-themes"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Palette,
  Bell,
  User,
  Check,
  Camera,
  Save,
  Shield,
  Mail,
  Globe,
  Lock,
} from "lucide-react"
import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"

// Schemas
const appearanceFormSchema = z.object({
  theme: z.enum(["light", "dark", "system"], {
    required_error: "Please select a theme.",
  }),
})

const profileFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters.").max(50, "Name must not exceed 50 characters."),
  email: z.string().email("Please enter a valid email address."),
  bio: z.string().max(160, "Bio must not exceed 160 characters.").optional(),
  language: z.string({ required_error: "Please select a language." }),
})

const notificationFormSchema = z.object({
  emailNotifications: z.boolean().default(true),
  pushNotifications: z.boolean().default(false),
  documentUpdates: z.boolean().default(true),
  caseReminders: z.boolean().default(true),
})


// Form Values Types
type AppearanceFormValues = z.infer<typeof appearanceFormSchema>
type ProfileFormValues = z.infer<typeof profileFormSchema>
type NotificationFormValues = z.infer<typeof notificationFormSchema>

// Simple theme preview
function ThemePreview({ theme, isSelected }: { theme: "light" | "dark" | "system"; isSelected: boolean }) {
  return (
    <div className={cn(
      "relative w-20 h-14 rounded-lg border-4 transition-all duration-200 cursor-pointer overflow-hidden",
      isSelected ? "border-neutral-900 dark:border-white shadow-lg" : "border-neutral-200 dark:border-neutral-700 hover:border-neutral-300 dark:hover:border-600",
      theme === "light" && "bg-white",
      theme === "dark" && "bg-neutral-900",
      theme === "system" && "bg-gradient-to-br from-white via-neutral-100 to-neutral-900"
    )}>
      {theme === "system" && (
        <div className="w-full h-full rounded-sm overflow-hidden">
          <div className="w-full h-1/2 bg-white" />
          <div className="w-full h-1/2 bg-neutral-900" />
        </div>
      )}
      {isSelected && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/20">
          <Check className="w-6 h-6 text-white" />
        </div>
      )}
    </div>
  )
}

export default function SettingsPage() {
  const { setTheme, theme } = useTheme()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const appearanceForm = useForm<AppearanceFormValues>({
    resolver: zodResolver(appearanceFormSchema),
    defaultValues: {
      theme: (theme as "light" | "dark" | "system") || "system",
    },
  })

  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: "John Doe",
      email: "<EMAIL>",
      bio: "Legal professional specializing in corporate law.",
      language: "en",
    },
  })

  const notificationForm = useForm<NotificationFormValues>({
    resolver: zodResolver(notificationFormSchema),
    defaultValues: {
      emailNotifications: true,
      pushNotifications: false,
      documentUpdates: true,
      caseReminders: true,
    },
  })

  function onAppearanceSubmit(data: AppearanceFormValues) {
    setTheme(data.theme)
    toast({
      title: "Theme updated",
      description: `Switched to ${data.theme} theme.`,
    })
  }

  function onProfileSubmit(data: ProfileFormValues) {
    toast({
      title: "Profile updated",
      description: "Your profile information has been saved.",
    })
  }

  function onNotificationSubmit(data: NotificationFormValues) {
    toast({
      title: "Notifications updated",
      description: "Your notification settings have been saved.",
    })
  }

  if (!mounted) {
    return null
  }
  
  return (
    <div className="flex-1 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-light tracking-tight text-neutral-900 dark:text-white">
              Settings
            </h1>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 font-light">
              Manage your account and preferences
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-8">
            {/* Profile Section */}
            <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
              <CardHeader>
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <img
                      src="/placeholder-user.jpg"
                      alt="User Avatar"
                      className="w-20 h-20 rounded-full border-8 border-white dark:border-neutral-800 shadow-lg"
                    />
                    <Button
                      size="icon"
                      variant="outline"
                      className="absolute bottom-0 right-0 rounded-full h-8 w-8 bg-white dark:bg-neutral-800 hover:bg-neutral-100 dark:hover:bg-neutral-700"
                    >
                      <Camera className="w-4 h-4" />
                    </Button>
                  </div>
                  <div>
                    <CardTitle className="text-2xl font-bold">Edit Profile</CardTitle>
                    <CardDescription>Update your personal information and preferences.</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <Form {...profileForm}>
                  <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-8">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={profileForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium">Full Name</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="e.g., John Doe" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={profileForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-sm font-medium">Email Address</FormLabel>
                            <FormControl>
                              <Input {...field} type="email" placeholder="e.g., <EMAIL>" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <FormField
                      control={profileForm.control}
                      name="bio"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium">Biography</FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              placeholder="Tell us a little about yourself..."
                              rows={4}
                              className="resize-none"
                            />
                          </FormControl>
                          <FormDescription>
                            A brief description of your professional background and interests.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={profileForm.control}
                      name="language"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm font-medium">Preferred Language</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a language" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="en">English (United States)</SelectItem>
                              <SelectItem value="es">Spanish (Spain)</SelectItem>
                              <SelectItem value="fr">French (France)</SelectItem>
                              <SelectItem value="de">German (Germany)</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            This will be your default language across the platform.
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <div className="flex justify-end gap-4 pt-4">
                      <Button variant="outline">Cancel</Button>
                      <Button type="submit" className="bg-neutral-900 hover:bg-neutral-800 dark:bg-white dark:hover:bg-neutral-100 text-white dark:text-neutral-900">
                        <Save className="w-4 h-4 mr-2" />
                        Save Changes
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </div>

          {/* Right Column */}
          <div className="space-y-8">
            {/* Appearance Section */}
            <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Palette className="w-6 h-6 text-neutral-500" />
                  <div>
                    <CardTitle>Appearance</CardTitle>
                    <CardDescription>Customize the look and feel.</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Form {...appearanceForm}>
                  <form onSubmit={appearanceForm.handleSubmit(onAppearanceSubmit)} className="space-y-6">
                    <FormField
                      control={appearanceForm.control}
                      name="theme"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Theme</FormLabel>
                          <FormDescription>
                            Select the theme for your dashboard.
                          </FormDescription>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              value={field.value}
                              className="flex gap-4 pt-2"
                            >
                              {(['light', 'dark', 'system'] as const).map((themeValue) => (
                                <FormItem key={themeValue} className="flex flex-col items-center space-y-2">
                                  <FormControl>
                                    <RadioGroupItem value={themeValue} className="sr-only" />
                                  </FormControl>
                                  <FormLabel className="cursor-pointer flex flex-col items-center gap-2">
                                    <ThemePreview theme={themeValue} isSelected={field.value === themeValue} />
                                    <span className="text-xs text-neutral-600 dark:text-neutral-400 capitalize">
                                      {themeValue}
                                    </span>
                                  </FormLabel>
                                </FormItem>
                              ))}
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button type="submit" className="bg-neutral-900 hover:bg-neutral-800 dark:bg-white dark:hover:bg-neutral-100 text-white dark:text-neutral-900">
                      <Save className="w-4 h-4 mr-2" />
                      Apply theme
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>

            {/* Notifications Section */}
            <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <Bell className="w-6 h-6 text-neutral-500" />
                  <div>
                    <CardTitle>Notifications</CardTitle>
                    <CardDescription>Manage your email notifications.</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Form {...notificationForm}>
                  <form onSubmit={notificationForm.handleSubmit(onNotificationSubmit)} className="space-y-4">
                    <FormField
                      control={notificationForm.control}
                      name="emailNotifications"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel>Email Notifications</FormLabel>
                            <FormDescription>
                              Receive notifications via email.
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={notificationForm.control}
                      name="pushNotifications"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel>Push Notifications</FormLabel>
                            <FormDescription>
                              Receive push notifications in browser.
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={notificationForm.control}
                      name="documentUpdates"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel>Document Updates</FormLabel>
                            <FormDescription>
                              Get notified when documents are updated.
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={notificationForm.control}
                      name="caseReminders"
                      render={({ field }) => (
                        <FormItem className="flex items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel>Case Reminders</FormLabel>
                            <FormDescription>
                              Receive reminders about upcoming deadlines.
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <Button type="submit" className="bg-neutral-900 hover:bg-neutral-800 dark:bg-white dark:hover:bg-neutral-100 text-white dark:text-neutral-900">
                      <Save className="w-4 h-4 mr-2" />
                      Save preferences
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}