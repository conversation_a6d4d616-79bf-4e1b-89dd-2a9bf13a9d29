"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, XAxis, YAxis, CartesianGrid, Tooltip, Legend, Cell } from "recharts"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  TrendingUp, 
  TrendingDown, 
  FileText, 
  Clock, 
  DollarSign, 
  Users, 
  Download,
  Calendar,
  Brain,
  CheckCircle,
  AlertTriangle,
  Activity
} from "lucide-react"

// Sample data for charts
const documentAnalysisData = [
  { month: "Jan", documents: 65, processed: 60, errors: 5 },
  { month: "Feb", documents: 78, processed: 75, errors: 3 },
  { month: "Mar", documents: 92, processed: 88, errors: 4 },
  { month: "Apr", documents: 87, processed: 85, errors: 2 },
  { month: "May", documents: 105, processed: 101, errors: 4 },
  { month: "Jun", documents: 125, processed: 122, errors: 3 },
]

const documentTypeData = [
  { name: "Contracts", value: 35, color: "hsl(var(--chart-1))" },
  { name: "Legal Briefs", value: 25, color: "hsl(var(--chart-2))" },
  { name: "Agreements", value: 20, color: "hsl(var(--chart-3))" },
  { name: "NDAs", value: 12, color: "hsl(var(--chart-4))" },
  { name: "Other", value: 8, color: "hsl(var(--chart-5))" },
]

const timeToProcessData = [
  { hour: "00:00", avgTime: 2.3 },
  { hour: "04:00", avgTime: 1.8 },
  { hour: "08:00", avgTime: 3.2 },
  { hour: "12:00", avgTime: 4.1 },
  { hour: "16:00", avgTime: 3.7 },
  { hour: "20:00", avgTime: 2.9 },
]

const riskAssessmentData = [
  { name: "Low Risk", value: 68, color: "hsl(var(--chart-2))" },
  { name: "Medium Risk", value: 24, color: "hsl(var(--chart-4))" },
  { name: "High Risk", value: 8, color: "hsl(var(--chart-5))" },
]

export default function AnalyticsPage() {
  return (
    <div className="flex-1 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-light tracking-tight text-neutral-900 dark:text-white">
              Analytics
            </h1>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 font-light">
              Insights and performance metrics for your legal AI platform
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Select defaultValue="30d">
              <SelectTrigger className="w-40 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white/90 dark:bg-neutral-900/90 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
              <FileText className="h-4 w-4 text-neutral-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,329</div>
              <div className="flex items-center text-xs text-green-600 dark:text-green-400">
                <TrendingUp className="h-3 w-3 mr-1" />
                +12.5% from last month
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Processing Time</CardTitle>
              <Clock className="h-4 w-4 text-neutral-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">2.4m</div>
              <div className="flex items-center text-xs text-green-600 dark:text-green-400">
                <TrendingDown className="h-3 w-3 mr-1" />
                -8.2% faster than last month
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Time Saved</CardTitle>
              <Activity className="h-4 w-4 text-neutral-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">847h</div>
              <div className="flex items-center text-xs text-green-600 dark:text-green-400">
                <TrendingUp className="h-3 w-3 mr-1" />
                +23.1% from last month
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Accuracy Rate</CardTitle>
              <Brain className="h-4 w-4 text-neutral-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">96.8%</div>
              <div className="flex items-center text-xs text-green-600 dark:text-green-400">
                <TrendingUp className="h-3 w-3 mr-1" />
                +0.3% from last month
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Document Processing Over Time */}
          <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Document Processing Trends
              </CardTitle>
              <CardDescription>
                Monthly document processing volume and success rates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={documentAnalysisData}>
                  <CartesianGrid strokeDasharray="3 3" className="stroke-neutral-200 dark:stroke-neutral-700" />
                  <XAxis dataKey="month" className="text-xs" />
                  <YAxis className="text-xs" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: 'hsl(var(--card))',
                      border: '1px solid hsl(var(--border))',
                      borderRadius: '8px',
                      fontSize: '12px'
                    }}
                  />
                  <Legend />
                  <Bar dataKey="processed" fill="hsl(var(--chart-1))" name="Processed Successfully" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="errors" fill="hsl(var(--chart-5))" name="Processing Errors" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Document Types Distribution */}
          <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Document Types Distribution
              </CardTitle>
              <CardDescription>
                Breakdown of document types processed this month
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <ResponsiveContainer width="60%" height={300}>
                  <PieChart>
                    <Pie
                      data={documentTypeData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {documentTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
                <div className="space-y-2">
                  {documentTypeData.map((entry, index) => (
                    <div key={entry.name} className="flex items-center gap-2 text-sm">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: entry.color }}
                      />
                      <span className="text-neutral-600 dark:text-neutral-400">
                        {entry.name}
                      </span>
                      <Badge variant="secondary" className="ml-auto text-xs">
                        {entry.value}%
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Bottom Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Processing Time by Hour */}
          <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Average Processing Time
              </CardTitle>
              <CardDescription>
                Processing time patterns throughout the day
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={timeToProcessData}>
                  <CartesianGrid strokeDasharray="3 3" className="stroke-neutral-200 dark:stroke-neutral-700" />
                  <XAxis dataKey="hour" className="text-xs" />
                  <YAxis className="text-xs" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: 'hsl(var(--card))',
                      border: '1px solid hsl(var(--border))',
                      borderRadius: '8px',
                      fontSize: '12px'
                    }}
                    formatter={(value) => [`${value} min`, 'Avg Time']}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="avgTime" 
                    stroke="hsl(var(--chart-3))" 
                    strokeWidth={3}
                    dot={{ r: 4, fill: 'hsl(var(--chart-3))' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Risk Assessment Summary */}
          <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Risk Assessment Summary
              </CardTitle>
              <CardDescription>
                Distribution of risk levels across analyzed documents
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {riskAssessmentData.map((risk, index) => (
                  <div key={risk.name} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: risk.color }}
                        />
                        <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                          {risk.name}
                        </span>
                      </div>
                      <span className="text-sm font-semibold text-neutral-900 dark:text-white">
                        {risk.value}%
                      </span>
                    </div>
                    <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2">
                      <div 
                        className="h-2 rounded-full transition-all duration-300" 
                        style={{ 
                          width: `${risk.value}%`, 
                          backgroundColor: risk.color 
                        }}
                      />
                    </div>
                  </div>
                ))}
                
                <div className="pt-4 mt-6 border-t border-neutral-200 dark:border-neutral-700">
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {riskAssessmentData[0].value}%
                      </div>
                      <div className="text-xs text-neutral-500">Documents flagged as low risk</div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-neutral-900 dark:text-white">
                        {documentAnalysisData[documentAnalysisData.length - 1].processed}
                      </div>
                      <div className="text-xs text-neutral-500">Documents processed this month</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}