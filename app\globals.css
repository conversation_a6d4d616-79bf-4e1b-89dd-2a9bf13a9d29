@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 2px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Dark mode scrollbar */
.dark .custom-scrollbar {
  scrollbar-color: rgba(75, 85, 99, 0.5) transparent;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}

body {
  font-family: var(--font-sans), sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    /* Light mode - Warm peachy palette */
    --background: 20 45% 98%; /* Almost white with warm undertone */
    --foreground: 12 60% 15%; /* Dark warm brown for text */
    --card: 24 80% 92%; /* Slightly darker than background */
    --card-foreground: 12 60% 20%;
    --popover: 24 85% 96%; /* Lighter than background */
    --popover-foreground: 12 60% 15%;
    --primary: 12 85% 65%; /* Vibrant coral */
    --primary-foreground: 0 0% 98%; /* Clean white */
    --secondary: 24 40% 88%; /* Muted peach */
    --secondary-foreground: 12 50% 25%;
    --muted: 24 30% 90%; /* Very subtle peach */
    --muted-foreground: 12 25% 45%; /* Medium contrast gray */
    --accent: 6 75% 72%; /* Slightly more pink accent */
    --accent-foreground: 12 60% 15%;
    --destructive: 0 75% 60%; /* Clear red */
    --destructive-foreground: 0 0% 98%;
    --border: 24 60% 82%; /* Soft peach border */
    --input: 24 50% 88%; /* Input background */
    --ring: 12 85% 60%; /* Focus ring */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 45%;
    --chart-3: 197 40% 35%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.75rem; /* Slightly more rounded */
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--accent);
    --sidebar-accent-foreground: var(--accent-foreground);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
  }
  
  .dark {
    /* Dark mode - Sophisticated purple/rose palette */
    --background: 270 15% 8%; /* Very dark purple, not pitch black */
    --foreground: 347 20% 90%; /* Bright rose text */
    --card: 270 8% 16%; /* Slightly lighter than background */
    --card-foreground: 347 18% 82%;
    --popover: 270 10% 14%; /* Darker than background */
    --popover-foreground: 347 20% 88%;
    --primary: 347 45% 68%; /* Vibrant rose */
    --primary-foreground: 270 8% 8%; /* Very dark purple */
    --secondary: 270 6% 22%; /* Muted purple */
    --secondary-foreground: 347 15% 78%;
    --muted: 270 5% 18%; /* Very subtle purple */
    --muted-foreground: 270 5% 65%; /* Medium contrast */
    --accent: 320 35% 62%; /* Pink-purple accent */
    --accent-foreground: 270 8% 12%;
    --destructive: 0 65% 58%; /* Softer red for dark mode */
    --destructive-foreground: 0 0% 98%;
    --border: 270 8% 25%; /* Subtle border */
    --input: 270 6% 20%; /* Input background */
    --ring: 347 45% 65%; /* Focus ring */
    --chart-1: 347 60% 65%;
    --chart-2: 180 45% 55%;
    --chart-3: 200 50% 60%;
    --chart-4: 50 65% 70%;
    --chart-5: 30 75% 65%;
    --radius: 0.75rem;
    --sidebar-background: var(--background);
    --sidebar-foreground: var(--foreground);
    --sidebar-primary: var(--primary);
    --sidebar-primary-foreground: var(--primary-foreground);
    --sidebar-accent: var(--accent);
    --sidebar-accent-foreground: var(--accent-foreground);
    --sidebar-border: var(--border);
    --sidebar-ring: var(--ring);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  /* Fix for Next.js - ensure html and body get the background */
  html {
    @apply bg-background;
  }
  
  body {
    @apply bg-background text-foreground transition-colors duration-200;
    min-height: 100vh;
  }
  
  /* Next.js root div styling */
  #__next {
    @apply bg-background min-h-screen;
  }
  
  /* Smooth transitions for theme switching */
  * {
    transition: border-color 200ms ease-in-out, background-color 200ms ease-in-out, color 200ms ease-in-out;
  }
  
  /* Custom text selection highlight */
  ::selection {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  /* Enhanced focus states */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
  
  /* Improved scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-accent rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-accent/80;
  }
}