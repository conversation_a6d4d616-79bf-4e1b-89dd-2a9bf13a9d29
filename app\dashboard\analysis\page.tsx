"use client"

import {
  <PERSON>,
  FileText,
  CheckCircle,
  Clock,
  Upload,
  Eye,
  Download,
  PlayCircle,
  Calendar,
  Hash,
  TrendingUp,
  Filter,
  Search,
  ChevronDown,
  ChevronUp,
  Hourglass,
} from "lucide-react"
import { useState } from "react"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import Link from "next/link"

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

export default function AnalysisPage() {
  const analyzedDocuments = [
    {
      id: "DOC001",
      name: "Corporate Merger Agreement - TechCorp",
      type: "Contract",
      status: "Processed",
      pages: 45,
      dateSubmitted: "2024-01-15",
      analysisDate: "2024-01-15",
      keyPoints: [
        "Purchase price: $2.5B with performance-based earnout",
        "Regulatory approval required within 12 months",
        "Key personnel retention clauses for C-suite",
        "Indemnification cap at $150M with 18-month survival period"
      ],
      riskLevel: "Medium",
      confidence: 94,
      summary: "Comprehensive merger agreement with standard terms and moderate risk exposure. All key provisions clearly defined.",
      tags: ["M&A", "Due Diligence", "Corporate Law"]
    },
    {
      id: "DOC002",
      name: "Employment Contract - Senior Developer",
      type: "Agreement",
      status: "Processed",
      pages: 12,
      dateSubmitted: "2024-01-12",
      analysisDate: "2024-01-12",
      keyPoints: [
        "Base salary: $180,000 with 15% annual bonus target",
        "Equity grant: 5,000 RSUs vesting over 4 years",
        "12-month non-compete clause in specific territories",
        "Intellectual property assignment comprehensive"
      ],
      riskLevel: "Low",
      confidence: 98,
      summary: "Standard employment agreement with competitive compensation and reasonable restrictive covenants.",
      tags: ["Employment", "HR", "Compensation"]
    },
    {
      id: "DOC003",
      name: "Software License Agreement - Enterprise",
      type: "License",
      status: "Processing",
      pages: 28,
      dateSubmitted: "2024-01-10",
      analysisDate: null,
      keyPoints: [],
      riskLevel: null,
      confidence: null,
      summary: "Document is currently being processed by AI analysis engine...",
      tags: ["Software", "Licensing", "IP"]
    },
    {
      id: "DOC004",
      name: "Real Estate Purchase Agreement",
      type: "Contract",
      status: "Processed",
      pages: 22,
      dateSubmitted: "2024-01-08",
      analysisDate: "2024-01-08",
      keyPoints: [
        "Purchase price: $3.2M with 20% down payment",
        "Inspection contingency expires in 14 days",
        "Title insurance requirement with enhanced coverage",
        "Closing date scheduled within 45 days"
      ],
      riskLevel: "Low",
      confidence: 96,
      summary: "Standard residential purchase agreement with typical contingencies and reasonable terms.",
      tags: ["Real Estate", "Property Law", "Transaction"]
    },
    {
      id: "DOC005",
      name: "Partnership Dissolution Agreement",
      type: "Agreement",
      status: "Pending",
      pages: 18,
      dateSubmitted: "2024-01-05",
      analysisDate: null,
      keyPoints: [],
      riskLevel: null,
      confidence: null,
      summary: "Document queued for analysis. Processing will begin shortly...",
      tags: ["Partnership", "Dissolution", "Business Law"]
    },
    {
      id: "DOC006",
      name: "Non-Disclosure Agreement - Vendor",
      type: "NDA",
      status: "Processed",
      pages: 8,
      dateSubmitted: "2024-01-03",
      analysisDate: "2024-01-03",
      keyPoints: [
        "Mutual confidentiality obligations for both parties",
        "3-year term with automatic renewal clause",
        "Carve-outs for publicly available information",
        "Return of materials obligation upon termination"
      ],
      riskLevel: "Low",
      confidence: 99,
      summary: "Standard mutual NDA with balanced terms and appropriate confidentiality protections.",
      tags: ["Confidentiality", "Vendor", "NDA"]
    }
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Processed":
        return (
          <Badge variant="default">
            <CheckCircle className="h-3 w-3 mr-1" />
            {status}
          </Badge>
        )
      case "Processing":
        return (
          <Badge variant="secondary">
            <Hourglass className="h-3 w-3 mr-1" />
            {status}
          </Badge>
        )
      case "Pending":
        return (
          <Badge variant="outline">
            <FileText className="h-3 w-3 mr-1" />
            {status}
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getRiskBadge = (riskLevel: string | null) => {
    if (!riskLevel) return null
    
    switch (riskLevel) {
      case "Low":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800">Low Risk</Badge>
      case "Medium":
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-300 dark:border-yellow-800">Medium Risk</Badge>
      case "High":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800">High Risk</Badge>
      default:
        return <Badge variant="outline">{riskLevel}</Badge>
    }
  }

  return (
    <div className="flex-1 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-light tracking-tight text-neutral-900 dark:text-white">
              Document Analysis
            </h1>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 font-light">
              AI-powered insights and analysis for your legal documents
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Select defaultValue="all">
              <SelectTrigger className="w-40 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white/90 dark:bg-neutral-900/90 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="processed">Processed</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
            <Link href="/dashboard/documents">
              <Button>
                <Upload className="h-4 w-4 mr-2" />
                Upload Document
              </Button>
            </Link>
          </div>
        </div>

        {/* Search */}
        <div className="mb-8 flex justify-center">
          <div className="relative max-w-md w-full">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-neutral-500 dark:text-neutral-400" />
            <Input
              placeholder="Search documents..."
              className="pl-10"
            />
          </div>
        </div>

        {/* Documents Grid */}
        <div className="space-y-6">
          {analyzedDocuments.map((doc) => (
            <Card
              key={doc.id}
              className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50 hover:border-neutral-300/70 dark:hover:border-neutral-700/70 transition-all duration-300"
            >
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="p-2 rounded-lg bg-neutral-100 dark:bg-neutral-800">
                        <Brain className="h-5 w-5 text-neutral-600 dark:text-neutral-400" />
                      </div>
                      <div>
                        <CardTitle className="text-xl font-medium text-neutral-900 dark:text-white line-clamp-1">
                          {doc.name}
                        </CardTitle>
                        <CardDescription className="text-sm text-neutral-600 dark:text-neutral-400">
                          Document ID: {doc.id}
                        </CardDescription>
                      </div>
                    </div>
                    
                    {/* Metadata Row */}
                    <div className="flex items-center gap-6 text-sm text-neutral-600 dark:text-neutral-400 mb-4">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        <span>{doc.type}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Hash className="h-4 w-4" />
                        <span>{doc.pages} pages</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>Submitted {doc.dateSubmitted}</span>
                      </div>
                    </div>

                    {/* Status and Risk Badges */}
                    <div className="flex items-center gap-3 mb-4">
                      {getStatusBadge(doc.status)}
                      {getRiskBadge(doc.riskLevel)}
                    </div>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {doc.tags.map((tag, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="text-xs bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-400"
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center gap-2 ml-6">
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={doc.status !== "Processed"}
                    >
                      <Eye className="h-4 w-4 mr-1" />
                      View Analysis
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={doc.status !== "Processed"}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Export Report
                    </Button>
                    <Button
                      size="sm"
                      disabled={doc.status !== "Processed"}
                    >
                      <PlayCircle className="h-4 w-4 mr-1" />
                      Review Now
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                {/* Summary */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-neutral-900 dark:text-white mb-2">
                    Analysis Summary
                  </h4>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed">
                    {doc.summary}
                  </p>
                </div>

                {/* Key Insights Accordion */}
                {doc.keyPoints.length > 0 && (
                  <Accordion type="single" collapsible className="w-full">
                    <AccordionItem value="item-1" className="border-4 border-neutral-200/50 dark:border-neutral-700/50 rounded-lg">
                      <AccordionTrigger>Key Insights</AccordionTrigger>
                      <AccordionContent>
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 pt-4">
                          {doc.keyPoints.map((point: string, index: number) => (
                            <div
                              key={index}
                              className="rounded-lg border border-neutral-200/50 dark:border-neutral-700/50 bg-white/70 dark:bg-neutral-800/70 p-4"
                            >
                              <div className="flex items-start gap-3">
                                <div className="flex-shrink-0 w-6 h-6 rounded-full bg-neutral-200 dark:bg-neutral-700 flex items-center justify-center text-xs font-medium text-neutral-600 dark:text-neutral-400">
                                  {index + 1}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <p className="text-sm text-neutral-700 dark:text-neutral-300 leading-relaxed">
                                    {point}
                                  </p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                )}

                {/* Processing State */}
                {(doc.status === "Processing" || doc.status === "Pending") && (
                  <div className="flex items-center justify-center py-8 text-neutral-500 dark:text-neutral-400">
                    <div className="text-center">
                      <Clock className="h-8 w-8 mx-auto mb-2 animate-spin" />
                      <p className="text-sm">
                        {doc.status === "Processing" 
                          ? "AI analysis in progress..." 
                          : "Document queued for processing..."
                        }
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State (if needed) */}
        {analyzedDocuments.length === 0 && (
          <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
            <CardContent className="flex flex-col items-center justify-center py-16 text-center">
              <Brain className="h-16 w-16 text-neutral-400 dark:text-neutral-600 mb-4" />
              <h3 className="text-xl font-medium text-neutral-900 dark:text-white mb-2">
                No Documents Analyzed Yet
              </h3>
              <p className="text-neutral-600 dark:text-neutral-400 mb-6 max-w-md">
                Upload your first document to see AI-powered analysis and insights here.
              </p>
              <Link href="/dashboard/documents">
                <Button>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Your First Document
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}