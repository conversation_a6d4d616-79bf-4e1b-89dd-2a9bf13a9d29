"use client"

import { useEffect, useRef, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Scale, FileText, Brain, Zap, ArrowRight, CheckCircle, Shield, Clock, Star, Users, TrendingUp, Award, PlayCircle, Sparkles } from "lucide-react"

function AnimatedGrid() {
  return (
    <div className="absolute inset-0 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-indigo-50/30 to-purple-50/50 dark:from-blue-950/20 dark:via-indigo-950/10 dark:to-purple-950/20" />
      <div className="absolute inset-0">
        {Array.from({ length: 20 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-px h-full bg-gradient-to-b from-transparent via-blue-200/30 to-transparent dark:via-blue-800/30"
            style={{ left: `${5 + i * 5}%` }}
          />
        ))}
        {Array.from({ length: 12 }).map((_, i) => (
          <div
            key={i}
            className="absolute w-full h-px bg-gradient-to-r from-transparent via-blue-200/30 to-transparent dark:via-blue-800/30"
            style={{ top: `${8 + i * 8}%` }}
          />
        ))}
      </div>
    </div>
  )
}

function FloatingOrbs() {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {Array.from({ length: 6 }).map((_, i) => (
        <div
          key={i}
          className="absolute rounded-full bg-gradient-to-r from-blue-400/20 to-purple-400/20 blur-xl animate-pulse"
          style={{
            width: `${100 + i * 50}px`,
            height: `${100 + i * 50}px`,
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${i * 2}s`,
            animationDuration: `${8 + i * 2}s`,
          }}
        />
      ))}
    </div>
  )
}

function GlowingCard({ children, className = "", delay = 0 }: { children: React.ReactNode, className?: string, delay?: number }) {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), delay)
    return () => clearTimeout(timer)
  }, [delay])

  return (
    <div
      ref={ref}
      className={`group relative p-8 rounded-3xl border border-white/20 dark:border-gray-800/50 bg-white/60 dark:bg-gray-900/60 backdrop-blur-xl hover:bg-white/80 dark:hover:bg-gray-900/80 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/20 ${className} ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}
      style={{ transition: 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)' }}
    >
      <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      <div className="absolute -inset-0.5 rounded-3xl bg-gradient-to-br from-blue-500/20 via-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-100 blur transition-opacity duration-500" />
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

function StatCard({ icon: Icon, value, label, gradient }: { icon: React.ElementType, value: string, label: string, gradient: string }) {
  return (
    <div className={`relative p-6 rounded-2xl bg-gradient-to-br ${gradient} text-white overflow-hidden group cursor-pointer hover:scale-105 transition-all duration-300`}>
      <div className="absolute inset-0 bg-black/10 group-hover:bg-black/5 transition-colors duration-300" />
      <div className="absolute -top-4 -right-4 w-20 h-20 rounded-full bg-white/10 group-hover:bg-white/20 transition-colors duration-300" />
      <div className="relative z-10">
        <Icon className="w-8 h-8 mb-4 opacity-90" />
        <div className="text-2xl font-bold mb-1">{value}</div>
        <div className="text-sm opacity-90">{label}</div>
      </div>
    </div>
  )
}

function HeroSection() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({ x: e.clientX, y: e.clientY })
    }
    window.addEventListener('mousemove', handleMouseMove)
    return () => window.removeEventListener('mousemove', handleMouseMove)
  }, [])

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      <AnimatedGrid />
      <FloatingOrbs />
      
      {/* Cursor follower */}
      <div 
        className="fixed w-96 h-96 rounded-full bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 blur-3xl pointer-events-none z-0 transition-all duration-1000"
        style={{
          left: mousePosition.x - 192,
          top: mousePosition.y - 192,
        }}
      />

      <div className="container mx-auto px-6 text-center relative z-10 pt-24">
        <div className="max-w-6xl mx-auto">
          {/* Badge */}
          <div className="inline-flex items-center gap-2 px-6 py-3 rounded-full border border-blue-200 dark:border-blue-800 bg-blue-50/80 dark:bg-blue-950/80 backdrop-blur-sm mb-8 hover:bg-blue-100/80 dark:hover:bg-blue-900/80 transition-all duration-300 group">
            <Sparkles className="w-4 h-4 text-blue-600 dark:text-blue-400 animate-pulse" />
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
              AI-Powered Legal Research Platform
            </span>
            <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse" />
          </div>

          {/* Main heading with gradient text */}
          <h1 className="text-6xl md:text-8xl lg:text-9xl font-bold tracking-tight mb-8 animate-in slide-in-from-bottom-10 duration-1000">
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 dark:from-blue-400 dark:via-purple-400 dark:to-pink-400 bg-clip-text text-transparent">
              Legal
            </span>
            <br />
            <span className="text-gray-900 dark:text-white font-light">Intelligence</span>
          </h1>

          {/* Subtitle */}
          <p className="text-xl md:text-2xl text-gray-600 dark:text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed animate-in slide-in-from-bottom-10 duration-1000 delay-200">
            Transform your legal practice with AI that researches, summarizes, and analyzes documents with 
            <span className="text-blue-600 dark:text-blue-400 font-semibold"> unprecedented precision</span> and 
            <span className="text-purple-600 dark:text-purple-400 font-semibold"> lightning speed</span>.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16 animate-in slide-in-from-bottom-10 duration-1000 delay-300">
            <Button
              size="lg"
              className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-xl hover:shadow-blue-500/25 group"
            >
              <Sparkles className="mr-2 w-5 h-5 group-hover:animate-spin" />
              Start Free Trial
              <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="px-8 py-4 border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 rounded-full font-semibold transition-all duration-300 hover:scale-105 group bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm"
            >
              <PlayCircle className="mr-2 w-5 h-5 group-hover:scale-110 transition-transform" />
              Watch Demo
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-3xl mx-auto animate-in slide-in-from-bottom-10 duration-1000 delay-500">
            <StatCard icon={Users} value="10K+" label="Legal Professionals" gradient="from-blue-500 to-blue-600" />
            <StatCard icon={FileText} value="2M+" label="Documents Analyzed" gradient="from-purple-500 to-purple-600" />
            <StatCard icon={TrendingUp} value="98%" label="Accuracy Rate" gradient="from-pink-500 to-pink-600" />
            <StatCard icon={Award} value="99.9%" label="Uptime SLA" gradient="from-indigo-500 to-indigo-600" />
          </div>
        </div>
      </div>
    </section>
  )
}

function FeaturesSection() {
  const features = [
    {
      icon: Brain,
      title: "AI Research Engine",
      description: "Scan millions of legal documents and precedents in seconds with our advanced neural networks and natural language processing.",
      gradient: "from-blue-500 to-cyan-500",
      stats: "2M+ documents",
    },
    {
      icon: FileText,
      title: "Smart Summarization",
      description: "Get precise, contextual summaries with key insights, critical information, and risk assessments highlighted automatically.",
      gradient: "from-purple-500 to-pink-500",
      stats: "99% accuracy",
    },
    {
      icon: Zap,
      title: "Instant Analysis",
      description: "Receive comprehensive reports with strategic recommendations, compliance checks, and actionable insights in real-time.",
      gradient: "from-orange-500 to-red-500",
      stats: "<10s response",
    },
  ]

  return (
    <section className="py-32 px-6 relative overflow-hidden bg-gradient-to-b from-white via-gray-50/50 to-white dark:from-gray-950 dark:via-gray-900/50 dark:to-gray-950">
      <div className="container mx-auto max-w-7xl relative z-10">
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full border border-purple-200 dark:border-purple-800 bg-purple-50 dark:bg-purple-950/50 text-purple-600 dark:text-purple-400 text-sm font-medium mb-6">
            <Star className="w-4 h-4" />
            Powerful Capabilities
          </div>
          <h2 className="text-5xl md:text-7xl font-bold tracking-tight mb-6">
            <span className="bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent">
              Revolutionary Features
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Experience the next generation of legal technology with AI-powered tools designed for modern law firms.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <GlowingCard key={index} delay={index * 200}>
              <div className="relative">
                {/* Icon with gradient background */}
                <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${feature.gradient} p-4 mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-full h-full text-white" />
                </div>

                {/* Stats badge */}
                <div className="absolute top-0 right-0 px-3 py-1 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 text-xs font-semibold">
                  {feature.stats}
                </div>

                {/* Content */}
                <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
                  {feature.description}
                </p>

                {/* Learn more link */}
                <button className="inline-flex items-center text-blue-600 dark:text-blue-400 font-semibold hover:text-blue-700 dark:hover:text-blue-300 transition-colors group/link">
                  Learn More
                  <ArrowRight className="ml-2 w-4 h-4 group-hover/link:translate-x-1 transition-transform" />
                </button>
              </div>
            </GlowingCard>
          ))}
        </div>

        {/* Additional feature highlights */}
        <div className="mt-20 grid md:grid-cols-2 gap-8">
          <GlowingCard delay={600}>
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-emerald-500 to-teal-500 p-3 flex-shrink-0">
                <Shield className="w-full h-full text-white" />
              </div>
              <div>
                <h4 className="text-xl font-bold mb-2 text-gray-900 dark:text-white">Enterprise Security</h4>
                <p className="text-gray-600 dark:text-gray-300">Bank-grade encryption, SOC2 compliance, and zero-trust architecture.</p>
              </div>
            </div>
          </GlowingCard>

          <GlowingCard delay={700}>
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-violet-500 to-purple-500 p-3 flex-shrink-0">
                <Clock className="w-full h-full text-white" />
              </div>
              <div>
                <h4 className="text-xl font-bold mb-2 text-gray-900 dark:text-white">Real-time Processing</h4>
                <p className="text-gray-600 dark:text-gray-300">Lightning-fast analysis with results delivered in seconds, not hours.</p>
              </div>
            </div>
          </GlowingCard>
        </div>
      </div>
    </section>
  )
}

function CTASection() {
  return (
    <section className="py-32 px-6 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-pink-900 dark:from-blue-950 dark:via-purple-950 dark:to-pink-950" />
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50" />
      
      <div className="container mx-auto max-w-5xl text-center relative z-10">
        <div className="animate-in slide-in-from-bottom-10 duration-1000">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full border border-white/20 bg-white/10 backdrop-blur-sm text-white/80 text-sm font-medium mb-8">
            <Sparkles className="w-4 h-4 animate-pulse" />
            Limited Time Offer
          </div>

          <h2 className="text-5xl md:text-7xl font-bold tracking-tight mb-8 text-white">
            Ready to Transform
            <br />
            <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
              Your Practice?
            </span>
          </h2>

          <p className="text-xl text-white/80 mb-12 max-w-3xl mx-auto leading-relaxed">
            Join thousands of legal professionals who have revolutionized their workflow with AI-powered document analysis.
            <strong className="text-white"> Start your free trial today.</strong>
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <Button
              size="lg"
              className="px-12 py-4 bg-white hover:bg-gray-100 text-gray-900 rounded-full font-bold transition-all duration-300 hover:scale-105 hover:shadow-2xl group"
            >
              <Sparkles className="mr-2 w-5 h-5 group-hover:animate-spin" />
              Get Started Free
              <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Button>

            <Button
              variant="outline"
              size="lg"
              className="px-8 py-4 border-2 border-white/30 hover:border-white/50 text-white hover:bg-white/10 rounded-full font-semibold transition-all duration-300 hover:scale-105"
            >
              Schedule Demo
            </Button>
          </div>

          {/* Enhanced trust indicators */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-8 max-w-2xl mx-auto">
            <div className="flex flex-col items-center gap-2 text-white/80">
              <CheckCircle className="w-6 h-6 text-green-400" />
              <span className="font-semibold">No Credit Card</span>
              <span className="text-sm">Required</span>
            </div>
            <div className="flex flex-col items-center gap-2 text-white/80">
              <Shield className="w-6 h-6 text-blue-400" />
              <span className="font-semibold">Enterprise Security</span>
              <span className="text-sm">SOC2 Compliant</span>
            </div>
            <div className="flex flex-col items-center gap-2 text-white/80">
              <Clock className="w-6 h-6 text-purple-400" />
              <span className="font-semibold">Setup in Minutes</span>
              <span className="text-sm">Get Started Today</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default function HomePage() {
  return (
    <div className="min-h-screen">
      <HeroSection />
      <FeaturesSection />
      <CTASection />
    </div>
  )
}