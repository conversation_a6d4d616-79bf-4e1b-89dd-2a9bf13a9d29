"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import {
  Home,
  Settings,
  FileText,
  BarChart3,
  Scale,
  ChevronLeft,
  ChevronRight,
  Sparkles,
  Calendar,
  Bell,
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

const sidebarItems = [
  { title: "Dashboard", href: "/dashboard", icon: Home },
  { title: "Documents", href: "/dashboard/documents", icon: FileText },
  { title: "Analysis", href: "/dashboard/analysis", icon: BarChart3 },
  { title: "Calendar", href: "/dashboard/calendar", icon: Calendar },
  
  { title: "Analytics", href: "/dashboard/analytics", icon: BarChart3 },
  { title: "Settings", href: "/dashboard/settings", icon: Settings },
]

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  const pathname = usePathname()

  const getActiveItemHref = (currentPathname: string) => {
    let activeItemHref = ''
    let longestMatchLength = 0

    for (const item of sidebarItems) {
      if (currentPathname === item.href) {
        return item.href
      }
      if (currentPathname.startsWith(item.href + '/') && item.href.length > longestMatchLength) {
        longestMatchLength = item.href.length
        activeItemHref = item.href
      }
    }
    return activeItemHref
  }

  const activeHref = getActiveItemHref(pathname)

  useEffect(() => {
    setMounted(true)
  }, [])

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed)
  }

  const sidebarVariants = {
    collapsed: {
      width: 88,
      transition: { type: "spring", stiffness: 200, damping: 25 },
    },
    expanded: {
      width: 280,
      transition: { type: "spring", stiffness: 200, damping: 25 },
    },
  }

  const navItemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: (i: number) => ({
      opacity: 1,
      x: 0,
      transition: {
        delay: i * 0.05,
        type: "spring",
        stiffness: 500,
        damping: 30,
      },
    }),
    exit: {
      opacity: 0,
      x: -20,
      transition: { type: "spring", stiffness: 400, damping: 25 },
    },
  }

  const logoVariants = {
    hidden: { opacity: 0, scale: 0.8, x: -20 },
    visible: {
      opacity: 1,
      scale: 1,
      x: 0,
      transition: { type: "spring", stiffness: 400, damping: 30, delay: 0.1 },
    },
    exit: { opacity: 0, scale: 0.8, x: -20, transition: { type: "spring", stiffness: 300, damping: 25 } },
  }

  const upgradeCardVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { type: "spring", stiffness: 400, damping: 30, delay: 0.2 },
    },
    exit: { opacity: 0, y: 20, scale: 0.95, transition: { type: "spring", stiffness: 300, damping: 25 } },
  }

  if (!mounted) {
    return null
  }

  return (
    <TooltipProvider delayDuration={100}>
      <motion.div
        initial={false}
        variants={sidebarVariants}
        animate={isCollapsed ? "collapsed" : "expanded"}
        className={cn(
          "relative flex flex-col h-full bg-white/60 dark:bg-neutral-900/60 backdrop-blur-xl border-r border-neutral-200/30 dark:border-neutral-800/30 shadow-lg shadow-neutral-900/5 dark:shadow-neutral-900/10",
          className
        )}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-white/20 to-transparent dark:from-neutral-800/20 pointer-events-none" />

        <div
          className={cn(
            "relative flex items-center border-b border-neutral-200/30 dark:border-neutral-800/30 transition-all duration-300",
            isCollapsed ? "p-4 justify-center" : "p-6 justify-between"
          )}
        >
          <AnimatePresence mode="wait" initial={false}>
            {!isCollapsed && (
              <motion.div
                key="logo-full"
                variants={logoVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="flex items-center gap-3"
              >
                <motion.div
                  className="relative p-2.5 rounded-xl bg-gradient-to-br from-neutral-100 to-neutral-50 dark:from-neutral-800 dark:to-neutral-900 border border-neutral-200/50 dark:border-neutral-700/50 shadow-sm"
                  whileHover={{ scale: 1.05, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                >
                  <Scale className="w-5 h-5 text-neutral-700 dark:text-neutral-200" />
                </motion.div>
                <div className="flex flex-col">
                  <span className="text-lg font-light text-neutral-800 dark:text-white tracking-tight">
                    Legal<span className="font-extralight text-neutral-600 dark:text-neutral-300">AI</span>
                  </span>
                  <span className="text-xs text-neutral-500 dark:text-neutral-400 font-light">
                    Workspace
                  </span>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleSidebar}
              className="relative p-2 hover:bg-neutral-100/80 dark:hover:bg-neutral-800/80 rounded-xl transition-all duration-200 h-9 w-9 border border-transparent hover:border-neutral-200/50 dark:hover:border-neutral-700/50"
            >
              <motion.div
                animate={{ rotate: isCollapsed ? 180 : 0 }}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
              >
                {isCollapsed ? (
                  <ChevronRight className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
                ) : (
                  <ChevronLeft className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
                )}
              </motion.div>
            </Button>
          </motion.div>
        </div>

        <nav
          className={cn(
            "relative flex-1 space-y-2 transition-all duration-300",
            isCollapsed ? "p-4" : "p-4"
          )}
        >
          <AnimatePresence initial={false}>
            {sidebarItems.map((item, index) => {
              const isActive = item.href === activeHref

              return (
                <motion.div
                  key={item.href}
                  custom={index}
                  variants={navItemVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                >
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Link href={item.href} passHref>
                        <motion.div
                          onHoverStart={() => setHoveredItem(item.href)}
                          onHoverEnd={() => setHoveredItem(null)}
                          whileTap={{ scale: 0.98 }}
                          className={cn(
                            "relative flex items-center rounded-xl transition-all duration-300 group cursor-pointer overflow-hidden",
                            isCollapsed ? "justify-center w-12 h-12 mx-auto" : "gap-4 px-4 py-3.5",
                            isActive
                              ? "text-neutral-900 dark:text-white bg-gradient-to-r from-neutral-100/80 to-neutral-50/80 dark:from-neutral-800/80 dark:to-neutral-900/80 border border-neutral-200/50 dark:border-neutral-700/50 shadow-sm"
                              : "text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white"
                          )}
                        >
                          {isActive && (
                            <motion.div
                              layoutId="activeIndicator"
                              className="absolute left-0 w-1 h-6 bg-gradient-to-b from-neutral-700 to-neutral-900 dark:from-neutral-300 dark:to-white rounded-r-full"
                              transition={{ type: "spring", stiffness: 400, damping: 30 }}
                            />
                          )}
                          <AnimatePresence>
                            {hoveredItem === item.href && !isActive && (
                              <motion.div
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 0.8 }}
                                className="absolute inset-0 bg-gradient-to-r from-neutral-100/60 to-neutral-50/60 dark:from-neutral-800/40 dark:to-neutral-900/40 rounded-xl"
                                transition={{ duration: 0.2 }}
                              />
                            )}
                          </AnimatePresence>
                          <motion.div
                            className="relative z-10 flex-shrink-0"
                            animate={{
                              scale: isActive ? 1.1 : 1,
                              rotate: hoveredItem === item.href ? 2 : 0,
                            }}
                            transition={{ type: "spring", stiffness: 400, damping: 20 }}
                          >
                            <item.icon
                              className={cn(
                                "w-5 h-5 transition-colors duration-200",
                                isActive
                                  ? "text-neutral-800 dark:text-neutral-100"
                                  : "text-neutral-500 dark:text-neutral-400 group-hover:text-neutral-700 dark:group-hover:text-neutral-200"
                              )}
                            />
                          </motion.div>
                          <AnimatePresence mode="wait">
                            {!isCollapsed && (
                              <motion.span
                                key="nav-text"
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                exit={{ opacity: 0, x: -10 }}
                                transition={{ type: "spring", stiffness: 400, damping: 25 }}
                                className={cn(
                                  "relative z-10 font-medium text-sm tracking-tight transition-colors duration-200 whitespace-nowrap",
                                  isActive
                                    ? "text-neutral-900 dark:text-white"
                                    : "text-neutral-600 dark:text-neutral-400 group-hover:text-neutral-800 dark:group-hover:text-neutral-200"
                                )}
                              >
                                {item.title}
                              </motion.span>
                            )}
                          </AnimatePresence>
                          {isActive && (
                            <motion.div
                              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent dark:via-white/5"
                              animate={{ x: [-100, 300] }}
                              transition={{ duration: 2.5, repeat: Infinity, repeatDelay: 2 }}
                            />
                          )}
                        </motion.div>
                      </Link>
                    </TooltipTrigger>
                    {isCollapsed && (
                      <TooltipContent
                        side="right"
                        sideOffset={8}
                        className="bg-white/80 dark:bg-neutral-900/80 backdrop-blur-xl border-neutral-200/30 dark:border-neutral-800/30 shadow-xl text-neutral-800 dark:text-white"
                      >
                        {item.title}
                      </TooltipContent>
                    )}
                  </Tooltip>
                </motion.div>
              )
            })}
          </AnimatePresence>
        </nav>

        <div className="relative p-4 mt-auto border-t border-neutral-200/30 dark:border-neutral-800/30">
          <AnimatePresence mode="wait" initial={false}>
            {!isCollapsed ? (
              <motion.div
                key="upgrade-card-full"
                variants={upgradeCardVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                className="relative overflow-hidden p-4 rounded-xl bg-gradient-to-br from-neutral-100/80 via-neutral-50/80 to-white/80 dark:from-neutral-800/80 dark:via-neutral-900/80 dark:to-neutral-950/80 border border-neutral-200/50 dark:border-neutral-700/50 shadow-lg backdrop-blur-sm"
              >
                <div className="absolute inset-0 opacity-5">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500" />
                </div>
                <div className="relative z-10">
                  <div className="flex items-center gap-2 mb-2">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                    >
                      <Sparkles className="w-4 h-4 text-neutral-700 dark:text-neutral-300" />
                    </motion.div>
                    <div className="text-sm font-medium text-neutral-900 dark:text-white">
                      Upgrade to Pro
                    </div>
                  </div>
                  <div className="text-xs text-neutral-600 dark:text-neutral-400 mb-3 leading-relaxed">
                    Unlock advanced AI features and unlimited document processing
                  </div>
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Button
                      size="sm"
                      className="w-full bg-gradient-to-r from-neutral-800 to-neutral-900 hover:from-neutral-700 hover:to-neutral-800 dark:from-white dark:to-neutral-100 dark:hover:from-neutral-100 dark:hover:to-neutral-200 text-white dark:text-neutral-900 shadow-lg transition-all duration-200 font-medium"
                    >
                      <Sparkles className="w-3 h-3 mr-2" />
                      Upgrade Now
                    </Button>
                  </motion.div>
                </div>
              </motion.div>
            ) : (
              <motion.div
                key="upgrade-card-collapsed"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
                className="flex justify-center"
              >
                <Tooltip>
                  <TooltipTrigger>
                    <motion.div whileHover={{ scale: 1.1, rotate: 5 }} whileTap={{ scale: 0.9 }}>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="p-2.5 rounded-xl hover:bg-neutral-100/80 dark:hover:bg-neutral-800/80 border border-transparent hover:border-neutral-200/50 dark:hover:border-neutral-700/50 transition-all duration-200"
                      >
                        <Sparkles className="w-5 h-5 text-neutral-600 dark:text-neutral-400" />
                      </Button>
                    </motion.div>
                  </TooltipTrigger>
                  <TooltipContent
                    side="right"
                    sideOffset={8}
                    className="bg-white/80 dark:bg-neutral-900/80 backdrop-blur-xl border-neutral-200/30 dark:border-neutral-800/30 shadow-xl text-neutral-800 dark:text-white"
                  >
                    Upgrade to Pro
                  </TooltipContent>
                </Tooltip>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </motion.div>
    </TooltipProvider>
  )
}
