"use client"

import {
  Upload,
  FileText,
  CheckCircle,
  Hourglass,
  Search,
  PlusCircle,
  MoreHorizontal,
} from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import Link from "next/link"

export default function DocumentsPage() {
  const allDocuments = [
    { id: "DOC001", name: "Contract Agreement Q3", type: "Contract", status: "Processed", date: "2023-07-15" },
    { id: "DOC002", name: "Client Brief - Project Alpha", type: "Brief", status: "Processing", date: "2023-07-14" },
    { id: "DOC003", name: "Legal Opinion on IP Rights", type: "Opinion", status: "Processed", date: "2023-07-12" },
    { id: "DOC004", name: "Discovery Request - Case 123", type: "Discovery", status: "Pending", date: "2023-07-10" },
    { id: "DOC005", name: "Partnership Agreement Draft", type: "Agreement", status: "Processed", date: "2023-07-08" },
  ]

  const processedDocuments = allDocuments.filter(doc => doc.status === "Processed")
  const processingDocuments = allDocuments.filter(doc => doc.status === "Processing" || doc.status === "Pending")

  return (
    <div className="flex-1 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-light tracking-tight text-neutral-900 dark:text-white">
              Documents
            </h1>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 font-light">
              Manage and review your legal documents
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button>
              <Upload className="h-4 w-4 mr-2" />
              Upload Document
            </Button>
          </div>
        </div>

        <main className="grid flex-1 items-start gap-8">
          <Tabs defaultValue="all" className="w-full">
            <div className="flex items-center justify-between mb-4">
              <TabsList className="grid w-full grid-cols-3 md:w-auto md:grid-cols-3 bg-neutral-100 dark:bg-neutral-800">
                <TabsTrigger value="all">All Documents</TabsTrigger>
                <TabsTrigger value="processed">Processed</TabsTrigger>
                <TabsTrigger value="processing">Processing</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all">
              <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
                <CardHeader>
                  <CardTitle>All Documents</CardTitle>
                  <CardDescription>A comprehensive list of all your uploaded documents.</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Document Name</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {allDocuments.map((doc) => (
                        <TableRow key={doc.id}>
                          <TableCell className="font-medium">{doc.name}</TableCell>
                          <TableCell>{doc.type}</TableCell>
                          <TableCell>
                            <Badge variant={doc.status === "Processed" ? "default" : doc.status === "Processing" ? "secondary" : "outline"}>
                              {doc.status === "Processed" && <CheckCircle className="h-3 w-3 mr-1" />}
                              {doc.status === "Processing" && <Hourglass className="h-3 w-3 mr-1" />}
                              {doc.status === "Pending" && <FileText className="h-3 w-3 mr-1" />}
                              {doc.status}
                            </Badge>
                          </TableCell>
                          <TableCell>{doc.date}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  aria-haspopup="true"
                                  size="icon"
                                  variant="ghost"
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Toggle menu</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>View</DropdownMenuItem>
                                <DropdownMenuItem>Download</DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>Delete</DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="processed">
              <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
                <CardHeader>
                  <CardTitle>Processed Documents</CardTitle>
                  <CardDescription>Documents that have been successfully analyzed by AI.</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {processedDocuments.map((doc) => (
                        <TableRow key={doc.id}>
                          <TableCell className="font-medium">{doc.name}</TableCell>
                          <TableCell>{doc.date}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  aria-haspopup="true"
                                  size="icon"
                                  variant="ghost"
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Toggle menu</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>View</DropdownMenuItem>
                                <DropdownMenuItem>Download</DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>Delete</DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                      {processedDocuments.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center text-neutral-500 dark:text-neutral-400">
                            No processed documents yet.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="processing">
              <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
                <CardHeader>
                  <CardTitle>Processing Documents</CardTitle>
                  <CardDescription>Documents currently being analyzed or awaiting processing.</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {processingDocuments.map((doc) => (
                        <TableRow key={doc.id}>
                          <TableCell className="font-medium">{doc.name}</TableCell>
                          <TableCell>
                            <Badge variant={doc.status === "Processing" ? "secondary" : "outline"}>
                              {doc.status === "Processing" && <Hourglass className="h-3 w-3 mr-1 animate-spin" />}
                              {doc.status === "Pending" && <FileText className="h-3 w-3 mr-1" />}
                              {doc.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  aria-haspopup="true"
                                  size="icon"
                                  variant="ghost"
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Toggle menu</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem>View</DropdownMenuItem>
                                <DropdownMenuItem>Cancel Processing</DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>Delete</DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                      {processingDocuments.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center text-neutral-500 dark:text-neutral-400">
                            No documents currently processing.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  )
}