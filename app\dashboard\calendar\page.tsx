"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, <PERSON><PERSON>T<PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { Plus, Ch<PERSON>ronL<PERSON><PERSON>, ChevronR<PERSON>, Trash2, Edit, Calendar as CalendarI<PERSON>, <PERSON>, Clock, MapPin, <PERSON>, Palette, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  type: "event" | "reminder";
  color: string;
}

const colors = [
  { name: "Blue", class: "bg-blue-500", text: "text-blue-500", hex: "hsl(var(--chart-1))" },
  { name: "Green", class: "bg-green-500", text: "text-green-500", hex: "hsl(var(--chart-2))" },
  { name: "Purple", class: "bg-purple-500", text: "text-purple-500", hex: "hsl(var(--chart-3))" },
  { name: "Red", class: "bg-red-500", text: "text-red-500", hex: "hsl(var(--chart-5))" },
  { name: "Orange", class: "bg-orange-500", text: "text-orange-500", hex: "hsl(var(--chart-4))" },
  { name: "Pink", class: "bg-pink-500", text: "text-pink-500", hex: "hsl(var(--chart-3))" }, // Reusing chart-3 for pink
  { name: "Indigo", class: "bg-indigo-500", text: "text-indigo-500", hex: "hsl(var(--chart-1))" }, // Reusing chart-1 for indigo
  { name: "Teal", class: "bg-teal-500", text: "text-teal-500", hex: "hsl(var(--chart-2))" }, // Reusing chart-2 for teal
];

const timeSlots = [
  "06:00", "06:30", "07:00", "07:30", "08:00", "08:30", "09:00", "09:30",
  "10:00", "10:30", "11:00", "11:30", "12:00", "12:30", "13:00", "13:30",
  "14:00", "14:30", "15:00", "15:30", "16:00", "16:30", "17:00", "17:30",
  "18:00", "18:30", "19:00", "19:30", "20:00", "20:30", "21:00", "21:30", "22:00"
];

export default function CalendarPage() {
  const [events, setEvents] = useState<Event[]>([]);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [isAddEditModalOpen, setIsAddEditModalOpen] = useState(false);
  const [isViewEventPopoverOpen, setIsViewEventPopoverOpen] = useState(false);
  const [newEvent, setNewEvent] = useState<Omit<Event, "id">>({
    title: "",
    description: "",
    date: "",
    time: "09:00",
    type: "event",
    color: "bg-blue-500",
  });
  const [editingEventId, setEditingEventId] = useState<string | null>(null);
  const [viewingEvent, setViewingEvent] = useState<Event | null>(null);
  const [filterType, setFilterType] = useState<"all" | "event" | "reminder">("all");
  const [isColorPickerOpen, setIsColorPickerOpen] = useState(false);
  const [isTimePickerOpen, setIsTimePickerOpen] = useState(false);

  useEffect(() => {
    const storedEvents = JSON.parse(localStorage?.getItem("calendarEvents") || "[]");
    setEvents(storedEvents);
  }, []);

  useEffect(() => {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem("calendarEvents", JSON.stringify(events));
    }
  }, [events]);

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDayOfMonth = new Date(year, month, 1);
    const lastDayOfMonth = new Date(year, month + 1, 0);
    const days = [];

    for (let i = 0; i < firstDayOfMonth.getDay(); i++) {
      days.push(null);
    }

    for (let i = 1; i <= lastDayOfMonth.getDate(); i++) {
      days.push(new Date(year, month, i));
    }
    return days;
  };

  const handlePrevMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() - 1, 1)
    );
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      (prev) => new Date(prev.getFullYear(), prev.getMonth() + 1, 1)
    );
  };

  const handleToday = () => {
    setCurrentMonth(new Date());
  };

  const handleOpenAddModal = (date?: Date, type: "event" | "reminder" = "event") => {
    setEditingEventId(null);
    setNewEvent({
      title: "",
      description: "",
      date: date ? format(date, "yyyy-MM-dd") : "",
      time: "09:00",
      type: type,
      color: type === "event" ? "bg-blue-500" : "bg-purple-500",
    });
    setIsAddEditModalOpen(true);
  };

  const handleOpenEditModal = (event: Event) => {
    setEditingEventId(event.id);
    setNewEvent({
      title: event.title,
      description: event.description,
      date: event.date,
      time: event.time,
      type: event.type,
      color: event.color,
    });
    setIsAddEditModalOpen(true);
    setIsViewEventPopoverOpen(false);
  };

  const handleSaveEvent = () => {
    if (!newEvent.title || !newEvent.date || !newEvent.time) {
      alert("Title, date, and time are required.");
      return;
    }

    if (editingEventId) {
      setEvents(
        events.map((event) =>
          event.id === editingEventId ? { ...newEvent, id: editingEventId } : event
        )
      );
    } else {
      setEvents([
        ...events,
        { ...newEvent, id: Date.now().toString() },
      ]);
    }
    setIsAddEditModalOpen(false);
  };

  const handleDeleteEvent = (id: string) => {
    if (confirm("Are you sure you want to delete this event?")) {
      setEvents(events.filter((event) => event.id !== id));
      setIsViewEventPopoverOpen(false);
    }
  };

  const getEventsForDay = (date: Date) => {
    const dateString = format(date, "yyyy-MM-dd");
    return events
      .filter((event) => event.date === dateString)
      .filter((event) => filterType === "all" || event.type === filterType)
      .sort((a, b) => a.time.localeCompare(b.time));
  };

  const selectedColor = colors.find(c => c.class === newEvent.color);

  const renderCalendarDays = () => {
    const days = getDaysInMonth(currentMonth);
    const today = format(new Date(), "yyyy-MM-dd");

    return days.map((day, index) => {
      if (!day) {
        return (
          <div
            key={`empty-${index}`}
            className="border border-neutral-100 dark:border-neutral-800/50 rounded-xl p-3 h-28 bg-gradient-to-br from-neutral-50/50 to-neutral-100/30 dark:from-neutral-900/30 dark:to-neutral-950/50"
          ></div>
        );
      }

      const dayEvents = getEventsForDay(day);
      const isToday = format(day, "yyyy-MM-dd") === today;
      const hasEvents = dayEvents.length > 0;

      return (
        <Card
          key={format(day, "yyyy-MM-dd")}
          className={cn(
            "relative border rounded-xl p-3 h-28 flex flex-col overflow-hidden cursor-pointer group transition-all duration-300 hover:scale-[1.02] hover:shadow-lg",
            "bg-gradient-to-br from-white/80 to-neutral-50/60 dark:from-neutral-900/80 dark:to-neutral-950/60 backdrop-blur-xl",
            isToday
              ? "border-blue-400 dark:border-blue-500 ring-2 ring-blue-400/30 dark:ring-blue-500/30 shadow-lg shadow-blue-500/10"
              : hasEvents
              ? "border-neutral-200/80 dark:border-neutral-700/80 shadow-sm"
              : "border-neutral-150 dark:border-neutral-800/50",
            "hover:border-neutral-300/80 dark:hover:border-neutral-600/80"
          )}
          onClick={() => handleOpenAddModal(day)}
        >
          <div className="flex justify-between items-center mb-2">
            <div className="flex items-center gap-2">
              <span className={cn(
                "text-sm font-semibold leading-none",
                isToday 
                  ? "text-blue-600 dark:text-blue-400" 
                  : "text-neutral-800 dark:text-neutral-200"
              )}>
                {day.getDate()}
              </span>
              {isToday && (
                <div className="w-1.5 h-1.5 rounded-full bg-blue-500 animate-pulse" />
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "opacity-0 group-hover:opacity-100 transition-all duration-200 h-6 w-6 p-0",
                "hover:bg-neutral-100/80 dark:hover:bg-neutral-700/50"
              )}
              onClick={(e) => {
                e.stopPropagation();
                handleOpenAddModal(day);
              }}
            >
              <Plus className="w-3 h-3" />
            </Button>
          </div>
          <div className="flex-1 overflow-y-auto space-y-1">
            {dayEvents.slice(0, 2).map((event, idx) => (
              <Dialog key={event.id} open={viewingEvent?.id === event.id && isViewEventPopoverOpen} onOpenChange={setIsViewEventPopoverOpen}>
                <DialogTrigger asChild>
                  <div
                    className={cn(
                      "text-[10px] px-1.5 py-0.5 rounded-md cursor-pointer truncate transition-all duration-200 transform hover:scale-105",
                      event.color,
                      "text-white font-medium shadow-sm",
                      "hover:shadow-md"
                    )}
                    onClick={(e) => {
                      e.stopPropagation();
                      setViewingEvent(event);
                    }}
                  >
                    {event.title}
                  </div>
                </DialogTrigger>
                <DialogContent className="w-80 bg-white/90 dark:bg-neutral-900/90 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50 p-4">
                  <div className="grid gap-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-bold text-lg">{viewingEvent?.title}</h4>
                      <Badge className={cn(viewingEvent?.color, "text-white")}>
                        {viewingEvent?.type}
                      </Badge>
                    </div>
                    <p className="text-sm text-neutral-600 dark:text-neutral-400">
                      {viewingEvent?.description || "No description."}
                    </p>
                    <div className="text-sm text-neutral-700 dark:text-neutral-300">
                      <p>Date: {viewingEvent?.date}</p>
                      <p>Time: {viewingEvent?.time}</p>
                    </div>
                    <div className="flex justify-end gap-2 mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => viewingEvent && handleOpenEditModal(viewingEvent)}
                      >
                        <Edit className="w-4 h-4 mr-2" /> Edit
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => viewingEvent && handleDeleteEvent(viewingEvent.id)}
                      >
                        <Trash2 className="w-4 h-4 mr-2" /> Delete
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            ))}
            {dayEvents.length > 2 && (
              <div className="text-[9px] text-neutral-500 dark:text-neutral-400 font-medium px-1.5 py-0.5 bg-neutral-100/80 dark:bg-neutral-800/80 rounded-md">
                +{dayEvents.length - 2} more
              </div>
            )}
          </div>
        </Card>
      );
    });
  };

  return (
    <div className="flex-1 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-light tracking-tight text-neutral-900 dark:text-white">
              Calendar
            </h1>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 font-light">
              Manage your events and reminders
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Select
              value={filterType}
              onValueChange={(value: "all" | "event" | "reminder") => setFilterType(value)}
            >
              <SelectTrigger className="w-[180px] bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent className="bg-white/90 dark:bg-neutral-900/90 backdrop-blur-xl border-4 border-neutral-200/50 dark:border-neutral-800/50">
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="event">Events</SelectItem>
                <SelectItem value="reminder">Reminders</SelectItem>
              </SelectContent>
            </Select>
            <Button
              onClick={() => handleOpenAddModal(undefined, "event")}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add New Event
            </Button>
            <Button
              onClick={() => handleOpenAddModal(undefined, "reminder")}
            >
              <Bell className="h-4 w-4 mr-2" />
              Add Reminder
            </Button>
          </div>
        </div>

        <Card className="bg-gradient-to-br from-white/70 via-white/50 to-neutral-50/30 dark:from-neutral-900/70 dark:via-neutral-900/50 dark:to-neutral-950/30 backdrop-blur-xl border border-neutral-200/30 dark:border-neutral-700/30 shadow-xl shadow-neutral-500/5 dark:shadow-black/20">
          <CardHeader className="pb-6 bg-gradient-to-r from-transparent via-white/20 to-transparent dark:via-neutral-800/20">
            <CardTitle className="flex justify-between items-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={handlePrevMonth}
                className="h-10 w-10 rounded-full hover:bg-neutral-100/80 dark:hover:bg-neutral-800/80 transition-all duration-200"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
              <div className="text-3xl font-bold bg-gradient-to-r from-neutral-800 to-neutral-600 dark:from-white dark:to-neutral-300 bg-clip-text text-transparent">
                {currentMonth.toLocaleString("default", {
                  month: "long",
                  year: "numeric",
                })}
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleNextMonth}
                className="h-10 w-10 rounded-full hover:bg-neutral-100/80 dark:hover:bg-neutral-800/80 transition-all duration-200"
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </CardTitle>
            <div className="flex justify-center mt-4">
              <Button
                variant="outline"
                onClick={handleToday}
                className="bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0 hover:from-blue-600 hover:to-blue-700 shadow-lg shadow-blue-500/20 transition-all duration-300"
              >
                <CalendarIcon className="w-4 h-4 mr-2" />
                Today
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-7 gap-2 text-center font-semibold text-neutral-700 dark:text-neutral-300 mb-4 py-3 bg-gradient-to-r from-neutral-50/50 via-white/30 to-neutral-50/50 dark:from-neutral-800/30 dark:via-neutral-900/20 dark:to-neutral-800/30 rounded-lg">
              {["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"].map((day, index) => (
                <div key={day} className="text-xs tracking-wider uppercase font-bold">
                  <span className="hidden lg:inline">{day}</span>
                  <span className="lg:hidden">{day.slice(0, 3)}</span>
                </div>
              ))}
            </div>
            <div className="grid grid-cols-7 gap-2">
              {renderCalendarDays()}
            </div>
          </CardContent>
        </Card>

        {/* Modern Google-Style Add/Edit Event Dialog */}
        <Dialog open={isAddEditModalOpen} onOpenChange={setIsAddEditModalOpen}>
          <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden bg-white/95 dark:bg-neutral-900/95 backdrop-blur-xl border border-neutral-200/50 dark:border-neutral-800/50 shadow-2xl p-0">
            {/* Header with colored accent */}
            <div className={cn("h-1.5 w-full", newEvent.color)} />
            
            <div className="p-6 space-y-6">
              {/* Title Section */}
              <div className="flex items-start gap-4">
                <div className={cn("p-3 rounded-full mt-1", newEvent.color, "bg-opacity-10")}>
                  {newEvent.type === "event" ? (
                    <CalendarIcon className={cn("w-5 h-5", selectedColor?.text)} />
                  ) : (
                    <Bell className={cn("w-5 h-5", selectedColor?.text)} />
                  )}
                </div>
                <div className="flex-1 space-y-1">
                  <Input
                    placeholder={newEvent.type === "event" ? "Add event title" : "Add reminder title"}
                    value={newEvent.title}
                    onChange={(e) => setNewEvent({ ...newEvent, title: e.target.value })}
                    className="text-xl font-medium border-none bg-transparent p-0 focus:ring-0 placeholder:text-neutral-400 h-auto"
                  />
                  <div className="h-px bg-neutral-200 dark:bg-neutral-700" />
                </div>
              </div>

              {/* Type Toggle */}
              <div className="flex items-center gap-2">
                <div className="flex bg-neutral-100 dark:bg-neutral-800 rounded-lg p-1">
                  <button
                    type="button"
                    onClick={() => setNewEvent({ ...newEvent, type: "event" })}
                    className={cn(
                      "px-4 py-2 text-sm font-medium rounded-md transition-all",
                      newEvent.type === "event"
                        ? "bg-white dark:bg-neutral-700 text-neutral-900 dark:text-white shadow-sm"
                        : "text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white"
                    )}
                  >
                    <CalendarIcon className="w-4 h-4 mr-2 inline" />
                    Event
                  </button>
                  <button
                    type="button"
                    onClick={() => setNewEvent({ ...newEvent, type: "reminder" })}
                    className={cn(
                      "px-4 py-2 text-sm font-medium rounded-md transition-all",
                      newEvent.type === "reminder"
                        ? "bg-white dark:bg-neutral-700 text-neutral-900 dark:text-white shadow-sm"
                        : "text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white"
                    )}
                  >
                    <Bell className="w-4 h-4 mr-2 inline" />
                    Reminder
                  </button>
                </div>
              </div>

              {/* Date & Time Row */}
              <div className="grid grid-cols-2 gap-4">
                {/* Date Picker */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal bg-neutral-50 dark:bg-neutral-800/50 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800",
                          !newEvent.date && "text-neutral-400"
                        )}
                      >
                        <CalendarIcon className="mr-3 h-4 w-4 text-neutral-500" />
                        {newEvent.date ? format(new Date(newEvent.date), "MMM d, yyyy") : "Select date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-xl border border-neutral-200/50 dark:border-neutral-800/50 shadow-xl">
                      <Calendar
                        mode="single"
                        selected={newEvent.date ? new Date(newEvent.date) : undefined}
                        onSelect={(date) => date && setNewEvent({ ...newEvent, date: format(date, "yyyy-MM-dd") })}
                        initialFocus
                        className="rounded-lg"
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Time Picker */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Time</Label>
                  <Popover open={isTimePickerOpen} onOpenChange={setIsTimePickerOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal bg-neutral-50 dark:bg-neutral-800/50 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800"
                      >
                        <Clock className="mr-3 h-4 w-4 text-neutral-500" />
                        {newEvent.time}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-56 p-3 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-xl border border-neutral-200/50 dark:border-neutral-800/50 shadow-xl">
                      <div className="grid grid-cols-2 gap-2 max-h-48 overflow-y-auto">
                        {timeSlots.map((time) => (
                          <button
                            key={time}
                            type="button"
                            onClick={() => {
                              setNewEvent({ ...newEvent, time });
                              setIsTimePickerOpen(false);
                            }}
                            className={cn(
                              "px-3 py-2 text-sm rounded-md text-left transition-all",
                              newEvent.time === time
                                ? "bg-blue-100 dark:bg-blue-900/50 text-blue-900 dark:text-blue-100"
                                : "hover:bg-neutral-100 dark:hover:bg-neutral-800 text-neutral-700 dark:text-neutral-300"
                            )}
                          >
                            {time}
                          </button>
                        ))}
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Description</Label>
                <Textarea
                  placeholder="Add description..."
                  value={newEvent.description}
                  onChange={(e) => setNewEvent({ ...newEvent, description: e.target.value })}
                  className="min-h-[80px] bg-neutral-50 dark:bg-neutral-800/50 border-neutral-200 dark:border-neutral-700 focus:border-neutral-400 dark:focus:border-neutral-600 resize-none"
                />
              </div>

              {/* Color Picker */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Color</Label>
                <div className="flex flex-wrap gap-3">
                  {colors.map((color) => (
                    <button
                      key={color.name}
                      type="button"
                      onClick={() => setNewEvent({ ...newEvent, color: color.class })}
                      className={cn(
                        "w-8 h-8 rounded-full transition-all hover:scale-110",
                        color.class,
                        newEvent.color === color.class && "ring-2 ring-neutral-400 dark:ring-neutral-500 ring-offset-2 dark:ring-offset-neutral-900"
                      )}
                      title={color.name}
                    />
                  ))}
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between p-6 bg-neutral-50/50 dark:bg-neutral-800/30">
              {editingEventId && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => editingEventId && handleDeleteEvent(editingEventId)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </Button>
              )}
              <div className="flex items-center gap-3 ml-auto">
                <Button
                  variant="ghost"
                  onClick={() => setIsAddEditModalOpen(false)}
                  className="text-neutral-600 hover:text-neutral-800 dark:text-neutral-400 dark:hover:text-neutral-200"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveEvent}
                  className={cn("px-6 text-white font-medium", newEvent.color, "hover:opacity-90")}
                  disabled={!newEvent.title || !newEvent.date || !newEvent.time}
                >
                  {editingEventId ? "Save changes" : "Create"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}